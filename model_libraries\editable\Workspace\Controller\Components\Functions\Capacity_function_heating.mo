within Workspace.Controller;
function Capacity_function_heating
  extends Modelica.Icons.Function;
  input Real OAT;
  input Real Target_cap;
  input Real Unit1;
  input Real Unit2;
  input Real Unit3;
  input Real Unit4;
  input Integer Number_of_module;
  output Real Target_Capacity;
  Real Max_Target_Cap40;
  Real Max_Target_Cap50;
  Real Max_Target_Cap60;
  Real Max_Target_Cap70;
  parameter Boolean is_ULN_option = false;
algorithm
  // Use noEvent to prevent Jacobian recalculations from conditional logic
  Max_Target_Cap40 := noEvent(if not is_ULN_option then (if OAT <= 280.15 then 38000 else 1356.2*OAT-342613) else (if OAT <= 280.15 then 31460 else 1356.2*OAT-342613));
  Max_Target_Cap50 := noEvent(if not is_ULN_option then (if OAT <= 280.15 then 48000 else 1660.7*OAT-418425) else (if OAT <= 280.15 then 38000 else 1660.7*OAT-418425));
  Max_Target_Cap60 := if not is_ULN_option then (if OAT <= 280.15 then 57500 else 2154.6*OAT-547745) else (if OAT <= 280.15 then 46464 else 2154.6*OAT-547745);
  Max_Target_Cap70 := if OAT <= 280.15 then 62000 else 62000;

  Target_Capacity := if Unit == 40 then min(Max_Target_Cap40,Target_cap) 
  else if Unit == 50 then min(Max_Target_Cap50,Target_cap)
  else if Unit == 60 then min(Max_Target_Cap60,Target_cap)
  else min(Max_Target_Cap70,Target_cap);
end Capacity_function_heating;
