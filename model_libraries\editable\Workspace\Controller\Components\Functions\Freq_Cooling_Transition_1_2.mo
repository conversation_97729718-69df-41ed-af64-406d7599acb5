within Workspace.Controller.Components.Functions;
function Freq_Cooling_Transition_1_2
  extends.Modelica.Icons.Function;
  input.Modelica.SIunits.Temperature T_lwt;
  input.Modelica.SIunits.Temperature T_oat;
  input Real freq_compressor;
  input Real target_cap;
  input Real current_cap;
  input Integer N_module;
  output Real freq_Transition;
  output Boolean Freq_Cooling_Transition_1_2;
  parameter Real intercept=90.96396983;
  parameter Real OAT_1=1.053321252;
  parameter Real LWT_1=-7.743799279;
  parameter Real OAT_1_LWT_2=0.002103908;
  parameter Real LWT_2=0.456956968;
  parameter Real LWT_3=-0.005187646;
  parameter Real OAT_2=-0.038808528;
  parameter Real OAT_3=0.00049929;
  parameter Real OAT_1_LWT_1=-0.042853065;
  parameter Real clamp=90;
protected
  .Modelica.SIunits.Temp_C OAT=T_lwt-273.15
    "Leaving water temperature in centigrade";
  .Modelica.SIunits.Temp_C LWT=T_oat-273.15
    "Outside air temperature in centigrade";
//   Real freq_Transition;
algorithm
  freq_Transition := min(
    intercept+OAT_1*OAT+LWT_1*LWT+OAT_1_LWT_2*OAT*LWT^2+LWT_2*LWT^2+LWT_3*LWT^3+OAT_2*OAT^2+OAT_3*OAT^3+OAT_1_LWT_1*OAT*LWT,
    clamp);
  // Add hysteresis and noEvent to prevent chattering and Jacobian recalculations
  Freq_Cooling_Transition_1_2 := noEvent((freq_compressor >= freq_Transition*1.05 or target_cap-15 > current_cap) and N_module > 1);
end Freq_Cooling_Transition_1_2;
