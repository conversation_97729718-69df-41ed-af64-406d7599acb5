within Workspace.System.Multimodule.HPH.R290;
model CL_61AQ_Modular
  extends.Workspace.Controller.StateMachine.StateMachine61AQ(
    UseStateMachine=true);
  extends.Workspace.System.Multimodule.HPH.System_61AQ_Modular(
    IsOFF1=not StateMachine.currentMode.Module_1,
    IsOFF2=not StateMachine.currentMode.Module_2,
    IsOFF3=not StateMachine.currentMode.Module_3,
    IsOFF4=not StateMachine.currentMode.Module_4,
    choiceBlock(
      Module_2_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_050,
      Module_3_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_120,
      Module_4_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_120,
      Number_of_modules=1,
      Module_1_selector=Workspace.Auxiliary.OptionBlock.Record_ModuleBase.Module_selector.Module_050,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_Option.Pump,
      Filter_selector=Workspace.Auxiliary.OptionBlock.FilterOption.Filter_selector.NONE),
    ECAT(
      TargetHeatingCapacity_W(
        setPoint=50000),
      HeatingAmbientAirDBTemp_K(
        setPoint=280.15),
      CondBrineEWT_K(
        setPoint=303.15),
      CondBrineLWT_K(
        setPoint=308.15),
      AmbientAirRH_nd(
        setPoint=0.87),
      HeatingAmbientAirWBTemp_K(
        setPoint=279.15)),
    Module_1(
      Module(
        BlockA(
          duct(
            Tdb_bounds={243.15,370})),
        BlockB(
          duct(
            Tdb_bounds={243.15,370})))),
    Module_2(
      Module(
        BlockA(
          duct(
            Tdb_bounds={243.15,370})),
        BlockB(
          duct(
            Tdb_bounds={243.15,370})))),
    Module_3(
      Module(
        BlockA(
          duct(
            Tdb_bounds={243.15,370})),
        BlockB(
          duct(
            Tdb_bounds={243.15,370})))),
    Module_4(
      Module(
        BlockA(
          duct(
            Tdb_bounds={243.15,370})),
        BlockB(
          duct(
            Tdb_bounds={243.15,370})))),
    use_bf=true,
    use_defrost=true,Use_EN14511 = false);
  import Freq_Heating_Transition_1_2=Workspace.Controller.Components.Functions.Freq_Heating_Transition_1_2;
  import Freq_Heating_Transition_2_3=Workspace.Controller.Components.Functions.Freq_Heating_Transition_2_3;
  import Freq_Heating_Transition_3_4=Workspace.Controller.Components.Functions.Freq_Heating_Transition_3_4;
  Real FreqTransition_1_2;
  Real FreqTransition_2_3;
  Real FreqTransition_3_4;
  Integer Stateid;
  parameter Boolean useStatePrediction = true;
  parameter Real StatePrediction_offset=15;

  // Discrete variables to break algebraic loop
  discrete Real FreqTransition_1_2_delayed;
  discrete Real FreqTransition_2_3_delayed;
  discrete Real FreqTransition_3_4_delayed;

initial equation
  // Initialize delayed transition frequencies with nominal values
  FreqTransition_1_2_delayed = 102.0;
  FreqTransition_2_3_delayed = 76.0;
  FreqTransition_3_4_delayed = 67.0;

equation
  // Update delayed transition frequencies discretely to break algebraic loop
  when sample(0, 0.1) then  // Update every 0.1 seconds
    FreqTransition_1_2_delayed = FreqTransition_1_2;
    FreqTransition_2_3_delayed = FreqTransition_2_3;
    FreqTransition_3_4_delayed = FreqTransition_3_4;
  end when;
  // Transition variables equations
  // Use delayed transition frequencies for state prediction to break algebraic loop
  Stateid=Workspace.Controller.Components.Functions.State_prediction(
    Module_1.CompressorFreq,
    choiceBlock.Number_of_modules,
    FreqTransition_1_2_delayed,  // Use delayed value to break algebraic loop
    FreqTransition_2_3_delayed,  // Use delayed value to break algebraic loop
    FreqTransition_3_4_delayed,  // Use delayed value to break algebraic loop
    Module_1.Module.capacity_design[1]+Module_1.Module.capacity_design[2],
    Module_2.Module.capacity_design[1]+Module_2.Module.capacity_design[2],
    Module_3.Module.capacity_design[1]+Module_3.Module.capacity_design[2],
    Module_4.Module.capacity_design[1]+Module_4.Module.capacity_design[2],
    ECAT.TargetHeatingCapacity_W.setPoint,
    ECAT.PubHeatingCapacity_W.value,
    StateMachine.currentMode.Module_2 or not useStatePrediction,
    StatePrediction_offset);
  (FreqTransition_1_2,Module_2_ON)=.Workspace.Controller.Components.Functions.Freq_Heating_Transition_1_2(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_1.CompressorFreq,
    ECAT.TargetHeatingCapacity_W.setPoint,
    ECAT.PubHeatingCapacity_W.value,
    choiceBlock.Number_of_modules);
  (FreqTransition_2_3,Module_3_ON)=.Workspace.Controller.Components.Functions.Freq_Heating_Transition_2_3(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_2.CompressorFreq,
    ECAT.TargetHeatingCapacity_W.setPoint,
    ECAT.PubHeatingCapacity_W.value,
    choiceBlock.Number_of_modules);
  (FreqTransition_3_4,Module_4_ON)=.Workspace.Controller.Components.Functions.Freq_Heating_Transition_3_4(
    node_out.T,
    ECAT.AmbientAirDBTemp_K.setPoint,
    Module_3.CompressorFreq,
    ECAT.TargetHeatingCapacity_W.setPoint,
    ECAT.PubHeatingCapacity_W.value,
    choiceBlock.Number_of_modules);
end CL_61AQ_Modular;
