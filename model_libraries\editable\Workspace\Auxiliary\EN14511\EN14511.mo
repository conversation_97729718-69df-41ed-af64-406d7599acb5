within Workspace.Auxiliary.EN14511;
model EN14511
  parameter Boolean integrated_pump=false;
  parameter Boolean glandless_circulator=false;
  parameter Boolean dry_motor_pump=false;
  parameter Real ie=1;
  //motor efficiency level defined in EC no 640/2009
  parameter Boolean heating_mode=true;
  parameter Real dp_bound=10
    "pressure drop bound to avoid divided by 0";
  //  input Real DPe;
  //  input Real DPi;
  //  input Real q;
  //
  //  input Real inst_gross_cap;
  //  input Real inst_gross_pow;
  //
  //  output Real inst_net_cap;
  //  output Real inst_net_pow;
  Modelica.Blocks.Interfaces.RealInput DPe(
    unit="Pa")
    annotation (Placement(transformation(extent={{-120,70},{-80,110}})));
  //[external static pressure difference Pa]
  Modelica.Blocks.Interfaces.RealInput DPi(
    unit="Pa")
    annotation (Placement(transformation(extent={{-120,30},{-80,70}})));
  //[internal static pressure difference Pa] negative
  Modelica.Blocks.Interfaces.RealInput q(
    unit="m3/s")
    annotation (Placement(transformation(extent={{-120,-10},{-80,30}})));
  //[liquid volume flow rate m3/s]
  Modelica.Blocks.Interfaces.RealInput inst_gross_cap(
    unit="W")
    annotation (Placement(transformation(extent={{-120,-50},{-80,-10}})));
  Modelica.Blocks.Interfaces.RealInput inst_gross_pow(
    unit="W")
    annotation (Placement(transformation(extent={{-120,-90},{-80,-50}})));
  Modelica.Blocks.Interfaces.RealOutput inst_net_cap(
    unit="W")
    annotation (Placement(transformation(extent={{90,20},{110,40}})));
  Modelica.Blocks.Interfaces.RealOutput inst_net_pow(
    unit="W")
    annotation (Placement(transformation(extent={{90,-40},{110,-20}})));
  Real P_hyd(
    unit="W");
  Real P_hyd_pump(
    unit="W");
  Real eta;
  Real eta_glandless;
  Real eta_dry;
  Real eta_pump;

  // Pre-computed terms for performance optimization
  Real P_hyd_pump_exp_term(start=1.0);
  Real P_hyd_log_term(start=1.0);
  Real P_hyd_power_term(start=1.0);
  Real C(
    unit="W");
  Real M(
    unit="W");
  Real C_pump(
    unit="W");
  Real M_pump(
    unit="W");
  Real C_glandless(
    unit="W");
  Real C_dry(
    unit="W");
  Real inst_net_cap_wopump(
    unit="W");
  Real inst_net_pow_wopump(
    unit="W");
  Real inst_net_cap_pump(
    unit="W");
  Real inst_net_pow_pump(
    unit="W");
equation
  P_hyd_pump=q*noEvent(max(dp_bound,DPe));
  P_hyd=q*noEvent(max(dp_bound,(-DPi)));

  // Pre-compute expensive mathematical operations
  P_hyd_pump_exp_term = noEvent(exp(-0.3*P_hyd_pump));
  P_hyd_log_term = noEvent(if P_hyd_pump > 500 then log(P_hyd_pump) else 0);
  P_hyd_power_term = noEvent(if P_hyd_pump <= 500 then P_hyd_pump^0.3183 else 0);

  // Optimized efficiency calculations with noEvent
  eta_glandless=(0.35844*P_hyd_pump*0.49/(1.7*P_hyd_pump+17*(1-P_hyd_pump_exp_term)))/0.23;

  eta_dry=noEvent(if P_hyd_pump > 500 then
    0.092*P_hyd_log_term-0.0403
  else
    0.0721*P_hyd_power_term);

  eta=noEvent(if P_hyd <= 300 then
    (0.35844*P_hyd*0.49/(1.7*P_hyd+17*(1-exp(-0.3*P_hyd))))/0.23
  elseif P_hyd > 500 then
    0.092*log(P_hyd)-0.0403
  else
    0.0721*(P_hyd^0.3183));
  ////Capacity correction
  //#pag 10
  C_glandless=P_hyd_pump/eta_glandless*(1-eta_glandless);
  //#[4]
  C_dry=P_hyd_pump/eta_dry*(ie-eta_dry);
  //#[5]
  C_pump=
    if glandless_circulator then
      C_glandless
    else
      C_dry;
  // Capacity correction with pump
  //#pag 11
  if P_hyd > 300 then
    C=P_hyd/eta*(0.85-eta);
  //#[7] Capacity correction without pump
  else
    C=P_hyd/eta*(1-eta);
  //  #[6] Capacity correction without pump
  end if;
  /////Power input correction    pag 12 4.1.5.3
  eta_pump=
    if glandless_circulator then
      eta_glandless
    else
      eta_dry;
  M_pump=P_hyd_pump/eta_pump;
  //#[10] power correction with pump
  M=P_hyd/eta;
  //#[11] power correction without pump
  ////Estimation inst_net_cap and inst_net_pow
  // pag 10   4.1.4.2.1
  inst_net_cap_pump=
    if heating_mode then
      inst_gross_cap-C_pump
    else
      inst_gross_cap+C_pump;
  inst_net_pow_pump=inst_gross_pow-M_pump;
  // pag 10   4.1.4.2.2
  inst_net_cap_wopump=
    if heating_mode then
      inst_gross_cap+C
    else
      inst_gross_cap-C;
  inst_net_pow_wopump=inst_gross_pow+M;
  inst_net_cap=
    if integrated_pump then
      inst_net_cap_pump
    else
      inst_net_cap_wopump;
  inst_net_pow=
    if integrated_pump then
      inst_net_pow_pump
    else
      inst_net_pow_wopump;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)),
    uses(
      Modelica(
        version="3.2.2")));
end EN14511;
