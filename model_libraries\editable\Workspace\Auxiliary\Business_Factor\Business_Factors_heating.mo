within Workspace.Auxiliary.Business_Factor;
model Business_Factors_heating
  parameter Boolean use_business_factor=true;
  parameter Boolean is_monobloc=true;
  parameter Integer Nrow=2;
  parameter Integer Heatcap_Tbiv=50000;
  Real bf_cap_interp40_50(
    start=1);
  Real bf_pow_interp40_50(
    start=1);
  Real bf_cap_interp60(
    start=1);
  Real bf_pow_interp60(
    start=1);
  Real bf_cap_interp70(
    start=1);
  Real bf_pow_interp70(
    start=1);
  Real bf_cap_interp80_100(
    start=1);
  Real bf_pow_interp80_100(
    start=1);
  Real bf_cap_interp120(
    start=1);
  Real bf_pow_interp120(
    start=1);
  Real bf_cap_interp140(
    start=1);
  Real bf_pow_interp140(
    start=1);
  parameter Real const_bf_cap=-5150.1699488455915;
  parameter Real load_bf_cap=-0.06492214515605722;
  parameter Real OAT_bf_cap=56.03254044620711;
  parameter Real OAT2_bf_cap=-0.20319757916979853;
  parameter Real OAT3_bf_cap=0.00024561973442076556;
  parameter Real const_bf_pow=3.617321798;
  parameter Real load_bf_pow=0.095265615;
  parameter Real OAT_bf_pow=-0.0097864797697;
  parameter Real OAT2_bf_pow=0.12149690069425928;
  parameter Real OAT3_bf_pow=0.00014549594213420189;

  // Pre-computed polynomial terms for performance optimization
  Real OAT_squared(start=OAT^2);
  Real OAT_cubed(start=OAT^3);
  Real load_squared(start=load^2);
  parameter Real max_bf_cap=0.05;
  parameter Real min_bf_cap=0;
  parameter Real max_bf_pow=1.025;
  parameter Real min_bf_pow=0.975;
public
  .Modelica.Blocks.Interfaces.RealInput net_pow
    annotation (Placement(transformation(extent={{-123.36832942853322,35.42953112948496},{-98.56820149216843,60.229659065849745}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput gross_pow
    annotation (Placement(transformation(extent={{-123.58208570271925,-16.519843693898515},{-98.78195776635447,8.280284242466273}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput gross_heat_cap
    annotation (Placement(transformation(extent={{-124.65086707364942,-43.24267806524137},{-99.85073913728463,-18.442550128876583}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput load
    annotation (Placement(transformation(extent={{-124.57326119877828,-72.94337236805433},{-99.7731332624135,-48.14324443168954}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput net_heat_cap
    annotation (Placement(transformation(extent={{-123.58208570271925,61.0835841298976},{-98.78195776635447,85.88371206626239}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_heat_cap
    annotation (Placement(transformation(extent={{100.7454437686318,48.30352914179285},{120.7454437686318,68.30352914179285}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_pow
    annotation (Placement(transformation(extent={{100.7454437686318,8.30352914179285},{120.7454437686318,28.30352914179285}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_heat_cap
    annotation (Placement(transformation(extent={{100.7454437686318,-31.69647085820715},{120.7454437686318,-11.69647085820715}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_pow
    annotation (Placement(transformation(extent={{100.7454437686318,-61.69647085820715},{120.7454437686318,-41.69647085820715}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput OAT
    annotation (Placement(transformation(extent={{-123.58208570271925,12.340553419304655},{-98.78195776635447,37.14068135566944}},rotation=0.0,origin={0.0,0.0})));
equation
  // Pre-compute polynomial terms for performance optimization
  OAT_squared = OAT*OAT;
  OAT_cubed = OAT_squared*OAT;
  load_squared = load*load;

  // Use noEvent and pre-computed terms to prevent Jacobian recalculations
  bf_cap_interp40_50=
    noEvent(if use_business_factor then
      max(
        min(
          const_bf_cap+load_bf_cap*load+OAT_bf_cap*OAT+OAT2_bf_cap*OAT_squared+OAT3_bf_cap*OAT_cubed,
          max_bf_cap),
        min_bf_cap)
    else
      0);
  bf_pow_interp40_50=
    noEvent(if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT_squared+OAT3_bf_pow*OAT_cubed,
          max_bf_pow),
        min_bf_pow)
    else
      1);
  bf_cap_interp60=
    noEvent(if use_business_factor then
      max(
        min(
          const_bf_cap+load_bf_cap*load+OAT_bf_cap*OAT+OAT2_bf_cap*OAT_squared+OAT3_bf_cap*OAT_cubed,
          0.04),
        0)
    else
      0);
  bf_pow_interp60=
    if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT*OAT+OAT3_bf_pow*OAT*OAT*OAT,
          1.02),
        0.97)
    else
      1;
  bf_cap_interp70=
    if use_business_factor then
      max(
        min(
          const_bf_cap+load_bf_cap*load+OAT_bf_cap*OAT+OAT2_bf_cap*OAT*OAT+OAT3_bf_cap*OAT*OAT*OAT,
          0.03),
        0)
    else
      0;
  bf_pow_interp70=
    if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT*OAT+OAT3_bf_pow*OAT*OAT*OAT,
          1.045),
        1)
    else
      1;  
  bf_cap_interp80_100=
    if use_business_factor then
      max(
        min(
          const_bf_cap+load_bf_cap*load+OAT_bf_cap*OAT+OAT2_bf_cap*OAT*OAT+OAT3_bf_cap*OAT*OAT*OAT,
          max_bf_cap),
        min_bf_cap)
    else
      0;
  bf_pow_interp80_100=
    if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT*OAT+OAT3_bf_pow*OAT*OAT*OAT,
          max_bf_pow),
        min_bf_pow)
    else
      1;
  bf_cap_interp120=
    if use_business_factor then
      max(
        min(
          2321.7705540133214-0.6875540095820322*load -25.57561822282674*OAT+0.09391719156697725*OAT*OAT-0.0001149536138246894*OAT*OAT*OAT+0.3847898432706986*load*load,
          0.04),
        -0.025)
    else
      0;
  bf_pow_interp120=
    if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT*OAT+OAT3_bf_pow*OAT*OAT*OAT,
          1.02),
        0.97)
    else
      1;
    
  bf_cap_interp140=
    if use_business_factor then
      max(
        min(
          const_bf_cap+load_bf_cap*load+OAT_bf_cap*OAT+OAT2_bf_cap*OAT*OAT+OAT3_bf_cap*OAT*OAT*OAT,
          0.04),
        0.0)
    else
      0;
  bf_pow_interp140=
    if use_business_factor then
      max(
        min(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*OAT+OAT2_bf_pow*OAT*OAT+OAT3_bf_pow*OAT*OAT*OAT,
          1.045),
        1)
    else
      1;  
  pub_net_heat_cap=
    noEvent(if is_monobloc then
      (
        if Nrow == 2 then
          (1+bf_cap_interp40_50)*net_heat_cap
        else (if Heatcap_Tbiv ==43499 then
          (1+bf_cap_interp60)*net_heat_cap
             else  (1+bf_cap_interp70)*net_heat_cap))
    else
      (
        if Nrow == 2 then
          (1+bf_cap_interp80_100)*net_heat_cap
        else
          (if Heatcap_Tbiv ==43499 then
          (1+bf_cap_interp120)*net_heat_cap
             else  (1+bf_cap_interp140)*net_heat_cap)));
  pub_gross_heat_cap=
    if is_monobloc then
      (
        if Nrow == 2 then
          (1+bf_cap_interp40_50)*gross_heat_cap
        else (if Heatcap_Tbiv==43499 then
          (1+bf_cap_interp60)*gross_heat_cap
             else (1+bf_cap_interp70)*gross_heat_cap))
    else
      (
        if Nrow == 2 then
          (1+bf_cap_interp80_100)*gross_heat_cap
        else (if Heatcap_Tbiv ==43499 then 
          (1+bf_cap_interp120)*gross_heat_cap
             else  (1+bf_cap_interp140)*gross_heat_cap));
  pub_net_pow=
    if is_monobloc then
      (
        if Nrow == 2 then
          net_pow*bf_pow_interp40_50
        else (if Heatcap_Tbiv==43499 then
          net_pow*bf_pow_interp60 
             else net_pow*bf_pow_interp70))
    else
      (
        if Nrow == 2 then
          net_pow*bf_pow_interp80_100
        else (if Heatcap_Tbiv==43499 then
          net_pow*bf_pow_interp120 
             else net_pow*bf_pow_interp140));
  pub_gross_pow=
    if is_monobloc then
      (
        if Nrow == 2 then
          gross_pow*bf_pow_interp40_50
        else (if Heatcap_Tbiv == 43499 then 
          gross_pow*bf_pow_interp60
             else gross_pow*bf_pow_interp70))
    else
      (
        if Nrow == 2 then
          gross_pow*bf_pow_interp80_100
        else (if Heatcap_Tbiv == 43499 then 
          gross_pow*bf_pow_interp120
             else gross_pow*bf_pow_interp140))
          ;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Rectangle(
          extent={{-100,101},{100,-101}},
          fillPattern=FillPattern.Solid,
          fillColor={80,227,194},
          origin={0,1}),
        Text(
          textString="Text",
          origin={-15,5},
          extent={{0.3868194842406787,-1.0225745767266825},{-1,1}}),
        Text(
          textString="BusinessFactors_heating",
          origin={0,-10},
          extent={{-100,78},{100,-78}})}));
end Business_Factors_heating;
