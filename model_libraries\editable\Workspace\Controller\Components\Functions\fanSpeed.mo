within Workspace.Controller.Components.Functions;
function fanSpeed
  "Fan speed setpoint calculation"
  extends Modelica.Icons.Function;
  import SI=Modelica.SIunits;
  input SI.Frequency compressorFrequency=85
    "Compressor speed [Hz]";
  input SI.Temperature T_lwt=273.15-2
    "Leaving water temperature";
  input SI.Temperature T_oat=273+35
    "Outside air temperature";
  input Real[9] coefficients={2.26358,8.491337,-0.023577,-0.016601,-0.020694,0.046051,0.026276,-0.050755,127.148697}
    "Coefficients array";
  input Real minfreq=100;
  input Real maxfreq=720;
  output Real fanSpeed;
protected
  SI.Temp_C T_lwt_degC=T_lwt-273.15
    "Leaving water temperature in centigrade";
  SI.Temp_C T_oat_degC=T_oat-273.15
    "Outside air temperature in centigrade";
  Real[9] variables
    "Variables array";
protected
  Real compFreq_squared = compressorFrequency*compressorFrequency;
  Real T_lwt_squared = T_lwt_degC*T_lwt_degC;
  Real T_oat_squared = T_oat_degC*T_oat_degC;
  Real cross_term1 = T_lwt_degC*compressorFrequency;
  Real cross_term2 = T_oat_degC*compressorFrequency;
  Real cross_term3 = T_oat_degC*T_lwt_degC;
algorithm
  // Use pre-computed terms for better performance
  variables := {T_lwt_degC,compressorFrequency,compFreq_squared,cross_term1,T_lwt_squared,cross_term2,cross_term3,T_oat_squared,1};
  fanSpeed := noEvent(min(
    max(
      minfreq,
      coefficients*variables),
    maxfreq))
    "min output for fan is 20Hz when no override on fans";
end fanSpeed;
