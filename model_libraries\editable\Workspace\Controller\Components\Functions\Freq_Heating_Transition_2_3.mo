within Workspace.Controller.Components.Functions;
function Freq_Heating_Transition_2_3
  extends.Modelica.Icons.Function;
  input.Modelica.SIunits.Temperature T_lwt;
  input.Modelica.SIunits.Temperature T_oat;
  input Real freq_compressor;
  input Real target_cap;
  input Real current_cap;
  input Integer N_module;
  output Real freq_Transition;
  output Boolean Freq_Heating_Transition_2_3;
  parameter Real intercept=71.9745567;
  parameter Real OAT_1=-5.179807655;
  parameter Real OAT_1_LWT_2=-0.000702224;
  parameter Real LWT_2=-0.019428305;
  parameter Real LWT_3=0.000352762;
  parameter Real OAT_2=0.262212802;
  parameter Real OAT_3=-0.003737113;
  parameter Real OAT_1_LWT_1=0.051735881;
  parameter Real clamp=90;
protected
  .Modelica.SIunits.Temp_C OAT=T_lwt-273.15
    "Leaving water temperature in centigrade";
  .Modelica.SIunits.Temp_C LWT=T_oat-273.15
    "Outside air temperature in centigrade";
algorithm
  freq_Transition := min(
    intercept+OAT_1*OAT+OAT_1_LWT_2*OAT*LWT^2+LWT_2*LWT^2+LWT_3*LWT^3+OAT_2*OAT^2+OAT_3*OAT^3+OAT_1_LWT_1*OAT*LWT,
    clamp);
  // Add hysteresis and noEvent to prevent chattering and Jacobian recalculations
  Freq_Heating_Transition_2_3 := noEvent((freq_compressor >= freq_Transition*1.05 or target_cap-15 > current_cap) and N_module > 2);
end Freq_Heating_Transition_2_3;
