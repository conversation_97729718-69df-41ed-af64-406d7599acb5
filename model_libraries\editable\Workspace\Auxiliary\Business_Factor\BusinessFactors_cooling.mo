within Workspace.Auxiliary.Business_Factor;
model BusinessFactors_cooling
  "Final BF implementation for cooling mode"
  parameter Boolean use_business_factor=true;
  parameter Real bf_cap_max=1
    "capacity business factor max value";
  parameter Real bf_cap_min=1
    "capacity business factor min value";
  parameter Real bf_pow_max=1
    "power business factor max value";
  parameter Real bf_pow_min=1
    "power business factor min value";
  parameter Real const_bf_cap=1
    "capacity business factor max value";
  parameter Real load_bf_cap=1
    "capacity business factor min value";
  parameter Real OAT_bf_cap=1
    "capacity business factor min value";
  parameter Real const_bf_pow=1
    "power business factor max value";
  parameter Real load_bf_pow=1
    "power business factor min value";
  parameter Real OAT_bf_pow=1
    "capacity business factor min value";
  Real bf_cap_interp(
    start=1);
  Real bf_pow_interp(
    start=1);
public
  .Modelica.Blocks.Interfaces.RealInput net_pow
    annotation (Placement(transformation(extent={{-100,30},{-60,70}})));
  .Modelica.Blocks.Interfaces.RealInput gross_pow
    annotation (Placement(transformation(extent={{-100,-30},{-60,10}})));
  .Modelica.Blocks.Interfaces.RealInput gross_cool_cap
    annotation (Placement(transformation(extent={{-100,-60},{-60,-20}})));
  .Modelica.Blocks.Interfaces.RealInput load
    annotation (Placement(transformation(extent={{-100.0,-98.0},{-60.0,-58.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput net_cool_cap
    annotation (Placement(transformation(extent={{-100,60},{-60,100}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_cool_cap
    annotation (Placement(transformation(extent={{80.0,50.0},{100.0,70.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_pow
    annotation (Placement(transformation(extent={{80,10},{100,30}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_cool_cap
    annotation (Placement(transformation(extent={{80,-30},{100,-10}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_pow
    annotation (Placement(transformation(extent={{80,-60},{100,-40}})));
  .Modelica.Blocks.Interfaces.RealInput LWT
    annotation (Placement(transformation(extent={{-100.0,-132.0},{-60.0,-92.0}},rotation=0.0,origin={0.0,0.0})));

equation
  // Use noEvent to prevent Jacobian recalculations from conditional logic
  bf_cap_interp=
    noEvent(if use_business_factor then
      min(
        max(
          (const_bf_cap+OAT_bf_cap*LWT),
          bf_cap_min),
        bf_cap_max)
    else
      1);
  bf_pow_interp=
    noEvent(if use_business_factor then
      min(
        max(
          const_bf_pow+load_bf_pow*load+OAT_bf_pow*LWT,
          bf_pow_min),
        bf_pow_max)
    else
      1);
  pub_net_cool_cap=bf_cap_interp*net_cool_cap;
  pub_gross_cool_cap=bf_cap_interp*gross_cool_cap;
  pub_net_pow=bf_pow_interp*net_pow;
  pub_gross_pow=bf_pow_interp*gross_pow;
  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={10,-12},
          extent={{-70,106},{70,-106}},
          fillPattern=FillPattern.Solid,
          fillColor={80,227,194}),
        Text(
          textString="BusinessFactors_cooling",
          origin={11,-15},
          extent={{-67,-47},{67,47}})}));
end BusinessFactors_cooling;
