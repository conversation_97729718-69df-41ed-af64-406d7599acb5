within Workspace.Controller.Components.Functions;
function Freq_Cooling_Transition_3_4
  extends.Modelica.Icons.Function;
  input.Modelica.SIunits.Temperature T_lwt;
  input.Modelica.SIunits.Temperature T_oat;
  input Real freq_compressor=70;
  input Real target_cap;
  input Real current_cap;
  input Integer N_module;
  output Real freq_Transition;
  output Boolean Freq_Cooling_Transition_3_4;
  parameter Real intercept=81.94801179;
  parameter Real LWT_1=-9.666778719;
  parameter Real OAT_2_LWT_1=-0.000508145;
  parameter Real LWT_2=0.612900591;
  parameter Real LWT_3=-0.010294585;
  parameter Real OAT_2=-0.01008817;
  parameter Real OAT_3=0.000274463;
  parameter Real OAT_1_LWT_1=0.023144561;
  parameter Real clamp=90;
protected
  .Modelica.SIunits.Temp_C OAT=T_lwt-273.15
    "Leaving water temperature in centigrade";
  .Modelica.SIunits.Temp_C LWT=T_oat-273.15
    "Outside air temperature in centigrade";
//   Real freq_Transition;
algorithm
  freq_Transition := min(
    intercept+LWT_1*LWT+OAT_2_LWT_1*OAT^2*LWT+LWT_2*LWT^2+LWT_3*LWT^3+OAT_2*OAT^2+OAT_3*OAT^3+OAT_1_LWT_1*OAT*LWT,
    clamp);
  // Add hysteresis and noEvent to prevent chattering and Jacobian recalculations
  Freq_Cooling_Transition_3_4 := noEvent((freq_compressor >= freq_Transition*1.05 or target_cap-15 > current_cap) and N_module > 3);
end Freq_Cooling_Transition_3_4;
